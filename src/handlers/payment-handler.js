const paymentService = require('../services/payment-service')
const paymentRepository = require('../repositories/payment-repository')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const logging = require('../common/logging')
const { PaymentType } = require('../common/constant')

class PaymentHandler {
  /**
   * Create a new payment order
   * Flow: POST /api/payments/create-order
   * Required for: Patient registration, consultation booking, prescription payment, lab test payment
   */
  async createOrder(req) {
    try {
      const paymentData = await req.json()
      
      // Validate required fields
      const requiredFields = ['amount', 'paymentType', 'patientId', 'organizationId', 'description']
      const missingFields = requiredFields.filter(field => !paymentData[field])
      
      if (missingFields.length > 0) {
        return jsonResponse(
          `Missing required fields: ${missingFields.join(', ')}`,
          HttpStatusCode.BadRequest
        )
      }
      
      // Validate payment type
      if (!Object.values(PaymentType).includes(paymentData.paymentType)) {
        return jsonResponse(
          `Invalid payment type. Allowed values: ${Object.values(PaymentType).join(', ')}`,
          HttpStatusCode.BadRequest
        )
      }
      
      // Convert amount to paise if provided in rupees
      if (paymentData.amount < 100) {
        paymentData.amount = Math.round(paymentData.amount * 100)
      }
      
      const result = await paymentService.createOrder(paymentData)
      
      return jsonResponse(result, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in createOrder handler', error)
      return jsonResponse(
        'Failed to create payment order',
        HttpStatusCode.InternalServerError
      )
    }
  }

  /**
   * Verify payment after Razorpay checkout
   * Flow: POST /api/payments/verify (called after successful Razorpay checkout)
   * Required for: All payment types after frontend payment completion
   */
  async verifyPayment(req) {
    try {
      const verificationData = await req.json()
      
      // Validate required fields
      const requiredFields = ['razorpay_order_id', 'razorpay_payment_id', 'razorpay_signature']
      const missingFields = requiredFields.filter(field => !verificationData[field])
      
      if (missingFields.length > 0) {
        return jsonResponse(
          `Missing required fields: ${missingFields.join(', ')}`,
          HttpStatusCode.BadRequest
        )
      }
      
      const result = await paymentService.verifyPayment(verificationData)
      
      if (result.verified) {
        return jsonResponse(result, HttpStatusCode.Ok)
      } else {
        return jsonResponse(result, HttpStatusCode.BadRequest)
      }
    } catch (error) {
      logging.logError('Error in verifyPayment handler', error)
      return jsonResponse(
        'Payment verification failed',
        HttpStatusCode.InternalServerError
      )
    }
  }

  /**
   * Handle Razorpay webhooks
   * Flow: POST /api/payments/webhook (called by Razorpay server)
   * Required for: Backup payment status updates when frontend flow fails
   */
  async handleWebhook(req) {
    try {
      const signature = req.headers.get('x-razorpay-signature')
      
      if (!signature) {
        return jsonResponse(
          'Missing webhook signature',
          HttpStatusCode.BadRequest
        )
      }
      
      const webhookData = await req.json()
      
      const result = await paymentService.handleWebhook(webhookData, signature)
      
      return jsonResponse(result, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in handleWebhook handler', error)
      return jsonResponse(
        'Webhook processing failed',
        HttpStatusCode.InternalServerError
      )
    }
  }

  /**
   * Get payment details by ID
   * Flow: GET /api/payments/details?paymentId=xxx
   * Required for: Checking payment status, displaying payment history
   */
  async getPaymentDetails(req) {
    try {
      const paymentId = req.query.get('paymentId')
      
      if (!paymentId) {
        return jsonResponse(
          'Missing paymentId parameter',
          HttpStatusCode.BadRequest
        )
      }
      
      const payment = await paymentService.getPaymentById(paymentId)
      
      return jsonResponse(payment, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getPaymentDetails handler', error)
      
      if (error.message.includes('not found')) {
        return jsonResponse(
          'Payment not found',
          HttpStatusCode.NotFound
        )
      }
      
      return jsonResponse(
        'Failed to fetch payment details',
        HttpStatusCode.InternalServerError
      )
    }
  }

  /**
   * Get payments by organization with pagination
   * Flow: GET /api/payments/organization?organizationId=xxx&pageSize=20&continuationToken=xxx
   * Required for: Organization payment history, admin dashboard
   */
  async getOrganizationPayments(req) {
    try {
      const organizationId = req.query.get('organizationId')
      const pageSize = parseInt(req.query.get('pageSize')) || 20
      const continuationToken = req.query.get('continuationToken')
      
      if (!organizationId) {
        return jsonResponse(
          'Missing organizationId parameter',
          HttpStatusCode.BadRequest
        )
      }
      
      const result = await paymentService.getPaymentsByOrganization(
        organizationId,
        pageSize,
        continuationToken
      )
      
      return jsonResponse(result, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getOrganizationPayments handler', error)
      return jsonResponse(
        'Failed to fetch organization payments',
        HttpStatusCode.InternalServerError
      )
    }
  }

  /**
   * Get payment statistics for organization
   * Flow: GET /api/payments/stats?organizationId=xxx
   * Required for: Financial reporting, dashboard analytics
   */
  async getPaymentStatistics(req) {
    try {
      const organizationId = req.query.get('organizationId')
      
      if (!organizationId) {
        return jsonResponse(
          'Missing organizationId parameter',
          HttpStatusCode.BadRequest
        )
      }
      
      const stats = await paymentService.getPaymentStatistics(organizationId)
      
      return jsonResponse(stats, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getPaymentStatistics handler', error)
      return jsonResponse(
        'Failed to fetch payment statistics',
        HttpStatusCode.InternalServerError
      )
    }
  }

  /**
   * Search payments with filters
   * Flow: GET /api/payments/search?organizationId=xxx&status=completed&paymentType=consultation
   * Required for: Advanced payment filtering and reporting
   */
  async searchPayments(req) {
    try {
      const filters = {
        organizationId: req.query.get('organizationId'),
        patientId: req.query.get('patientId'),
        status: req.query.get('status'),
        paymentType: req.query.get('paymentType'),
        startDate: req.query.get('startDate'),
        endDate: req.query.get('endDate'),
        minAmount: req.query.get('minAmount') ? parseInt(req.query.get('minAmount')) * 100 : null, // Convert to paise
        maxAmount: req.query.get('maxAmount') ? parseInt(req.query.get('maxAmount')) * 100 : null  // Convert to paise
      }
      
      // Remove null/undefined filters
      Object.keys(filters).forEach(key => {
        if (filters[key] === null || filters[key] === undefined || filters[key] === '') {
          delete filters[key]
        }
      })
      
      if (!filters.organizationId) {
        return jsonResponse(
          'Missing organizationId parameter',
          HttpStatusCode.BadRequest
        )
      }
      
      const payments = await paymentRepository.searchPayments(filters)
      
      return jsonResponse(payments, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in searchPayments handler', error)
      return jsonResponse(
        'Failed to search payments',
        HttpStatusCode.InternalServerError
      )
    }
  }
}

module.exports = new PaymentHandler()
