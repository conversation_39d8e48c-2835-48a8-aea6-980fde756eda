const { RecordStatus, PaymentType } = require('../common/constant')
const patientService = require('../services/patient-service')
const userService = require('../services/user-service')
const organizationService = require('../services/admin/organization-service')
const paymentService = require('../services/payment-service')
const { v4: uuidv4 } = require('uuid')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')

// Helper function to add organization filter to SQL queries
function addOrganizationFilter(query, organizationId) {
  const trimmedQuery = query.trim()

  if (trimmedQuery.toUpperCase().includes('WHERE')) {
    // Check if the WHERE clause contains OR conditions that need to be wrapped in parentheses
    const whereIndex = trimmedQuery.toUpperCase().indexOf('WHERE')
    const whereClause = trimmedQuery.substring(whereIndex + 5).trim()

    // If the WHERE clause contains OR conditions, wrap them in parentheses
    if (whereClause.toUpperCase().includes(' OR ')) {
      const selectPart = trimmedQuery.substring(0, whereIndex + 5)
      return `${selectPart} (${whereClause}) AND c.organizationId = "${organizationId}"`
    } else {
      return `${trimmedQuery} AND c.organizationId = "${organizationId}"`
    }
  } else {
    return `${trimmedQuery} WHERE c.organizationId = "${organizationId}"`
  }
}

class PatientHanlder {
  async CreatePatientProfile(patient, created_by) {
    if (!patient.id) {
      patient.id = uuidv4()
    }
    patient.created_by = created_by
    patient.updated_by = created_by

    const user = await userService.getUserById(created_by)
    if (user && user.organizationId) {
      patient.organizationId = user.organizationId
    } else {
      throw new Error('User organization not found. Cannot register patient.')
    }

    var res = await patientService.CreatePatientProfile(patient)
    return res
  }

  /**
   * Create patient with payment integration
   * Checks organization registration fee and creates payment order if required
   */
  async CreatePatientProfileWithPayment(patient, created_by) {
    try {
      // Get user and organization details
      const user = await userService.getUserById(created_by)
      if (!user || !user.organizationId) {
        throw new Error('User organization not found. Cannot register patient.')
      }

      // Get organization details to check registration fee
      const organization = await organizationService.getOrganizationById(
        user.organizationId,
      )
      if (!organization) {
        throw new Error('Organization not found.')
      }

      // Set patient organization
      patient.organizationId = user.organizationId

      // If organization has registration fee, create payment order first
      if (organization.registrationFee && organization.registrationFee > 0) {
        // Generate patient ID if not provided
        if (!patient.id) {
          patient.id = uuidv4()
        }

        // Create payment order for registration fee
        const paymentData = {
          amount: organization.registrationFee, // Amount in rupees
          currency: 'INR',
          paymentType: PaymentType.PATIENT_REGISTRATION,
          patientId: patient.id,
          organizationId: organization.id,
          description: `Registration Fee for ${patient.name}`,
          metadata: {
            patientName: patient.name,
            organizationName: organization.name,
          },
        }

        const paymentOrder = await paymentService.createOrder(paymentData)

        // Return payment order details for frontend to process
        return {
          requiresPayment: true,
          registrationFee: organization.registrationFee,
          paymentOrder: paymentOrder.data,
          patientId: patient.id,
          message: 'Payment required for patient registration',
        }
      } else {
        // No payment required, create patient directly
        return await this.CreatePatientProfile(patient, created_by)
      }
    } catch (error) {
      throw new Error(`Patient registration failed: ${error.message}`)
    }
  }

  /**
   * Complete patient registration after successful payment
   * Called after payment verification is successful
   */
  async CompletePatientRegistration(patientData, paymentId, created_by) {
    try {
      // Verify payment is completed
      const payment = await paymentService.getPaymentById(paymentId)
      if (!payment || !payment.isSuccessful()) {
        throw new Error('Payment not completed or not found')
      }

      // Ensure patient data matches payment
      if (payment.patientId !== patientData.id) {
        throw new Error('Patient ID mismatch with payment record')
      }

      // Create patient profile
      patientData.created_by = created_by
      patientData.updated_by = created_by
      patientData.organizationId = payment.organizationId

      const res = await patientService.CreatePatientProfile(patientData)

      return {
        success: true,
        patient: res,
        paymentId: paymentId,
        message: 'Patient registered successfully with payment',
      }
    } catch (error) {
      throw new Error(
        `Failed to complete patient registration: ${error.message}`,
      )
    }
  }

  async UpdatePatientProfile(patient, updated_by) {
    patient.updated_by = updated_by
    var res = await patientService.UpdatePatientProfile(patient)
    return res
  }

  async GetPatientProfile(patientId, userId) {
    const user = await userService.getUserById(userId)
    if (!user || !user.organizationId) {
      throw new Error('User organization not found. Cannot access patient.')
    }

    var data = await patientService.GetPatientProfile(patientId)

    if (data && data.organizationId !== user.organizationId) {
      throw new Error(
        'Access denied. Patient belongs to a different organization.',
      )
    }

    return data
  }

  async GetPatientProfiles(pageSize, continueToken, userId) {
    const user = await userService.getUserById(userId)
    if (!user || !user.organizationId) {
      throw new Error(
        'User organization not found. Cannot get patient profiles.',
      )
    }

    var data = await patientService.GetPatientProfilesByOrganization(
      user.organizationId,
      pageSize,
      continueToken,
    )
    return data
  }

  async SearchPatientProfiles(query, pageSize, continueToken, userId) {
    const user = await userService.getUserById(userId)

    if (!user || !user.organizationId) {
      throw new Error('User organization not found. Cannot search patients.')
    }

    const organizationFilteredQuery = addOrganizationFilter(
      query,
      user.organizationId,
    )

    var data = await patientService.SearchPatient(
      organizationFilteredQuery,
      pageSize,
      continueToken,
    )

    if (data?.items?.length > 0) {
      console.log(
        '  First Result Organization ID:',
        data.items[0].organizationId,
      )
    }

    return data
  }

  async GetPatientByQuery(queryString, userId) {
    // Get the user's organization ID to filter the query
    const user = await userService.getUserById(userId)
    if (!user || !user.organizationId) {
      throw new Error('User organization not found. Cannot query patients.')
    }

    // Modify the query to include organization filter
    const organizationFilteredQuery = addOrganizationFilter(
      queryString,
      user.organizationId,
    )

    var data = await patientService.QueryPatientProfile(
      organizationFilteredQuery,
    )
    return data
  }

  async GetPatientHistory(patientId) {
    var data = await patientService.GetPatientHistory(patientId)
    return data
  }

  async GetPatientHistoryByDate(patienId, startDate, endDate) {
    if (startDate === endDate) {
      startDate =
        new Date(startDate).toISOString().split('T')[0] + 'T00:00:00.000Z'
      endDate = new Date(endDate).toISOString().split('T')[0] + 'T23:59:59.999Z'
    } else {
      endDate = new Date(endDate).toISOString().split('T')[0] + 'T23:59:59.999Z'
    }

    var query = `SELECT * FROM c WHERE c.patientId = '${patienId}' and c.updated_on >= '${startDate}' and c.updated_on <= '${endDate}'`
    var data = await patientService.getPatientHistoryByQuey(query)
    return data
  }

  async CreatePatientHistory(patientId, patientHistory, created_by) {
    patientHistory.created_by = created_by
    patientHistory.updated_by = created_by
    patientHistory.status = RecordStatus.EDITABLE
    var res = await patientService.CreatePatientHistory(
      patientId,
      patientHistory,
    )
    return res
  }

  async updatePatientHistory(patientId, patientHistory, updated_by) {
    if (patientHistory.patientId !== patientId) {
      return null
    }
    patientHistory.updated_by = updated_by
    var res = await patientService.updatePatientHistory(patientHistory)
    return res
  }

  async GetPatientConsultations(patientId) {
    var data = await patientService.GetPatientConsultations(patientId)
    return data
  }

  async CreatePatientConsultations(
    patientId,
    patientConsultations,
    created_by,
  ) {
    patientConsultations.created_by = created_by
    patientConsultations.updated_by = created_by
    var res = await patientService.CreatePatientConsultation(
      patientId,
      patientConsultations,
    )
    return res
  }

  async UpdatePatientConsultations(patientConsultations, updated_by) {
    if (patientHistory.patientId !== patientId) {
      return null
    }
    patientConsultations.updated_by = updated_by
    var res = await patientService.UpdatePatientConsultations(
      patientConsultations,
    )
    return res
  }

  async CreatePatientConsultant(patienId, patientConsultant, created_by) {
    if (!patientConsultant.id) {
      patientConsultant.id = uuidv4()
    }
    patientConsultant.patientId = patienId
    patientConsultant.created_by = created_by
    patientConsultant.updated_by = created_by
    var res = await patientService.CreatePatientConsultant(
      patienId,
      patientConsultant,
    )
    return res
  }

  async UpdatePatientConsultant(patientConsultant, updated_by) {
    patientConsultant.updated_by = updated_by
    var res = await patientService.UpdatePatientConsultant(patientConsultant)
    return res
  }

  async GetPateintConsultant(patientId, date) {
    var data = await patientService.GetPatientConsultant(patientId, date)
    return data
  }

  async UpsertPatientProfile(patientId, payload, updated_by) {
    payload.updated_by = updated_by
    var data = await patientService.UpsertPatient(patientId, payload)
    return data
  }

  async fetchPatientsForOrganization(req) {
    try {
      const organizationId = req.query.get('organizationId')
      const searchText = req.query.get('searchText') || ''
      const pageSize = parseInt(req.query.get('pageSize')) || 10
      const page = parseInt(req.query.get('page')) || 1
      const sortBy = req.query.get('sortBy') || null
      const sortOrder = req.query.get('sortOrder') || 'asc'

      const filters = {
        gender: req.query.get('gender') || null,
        ageRange:
          req.query.get('fromAge') && req.query.get('toAge')
            ? [
                parseInt(req.query.get('fromAge')),
                parseInt(req.query.get('toAge')),
              ]
            : null,
        registrationDateRange:
          req.query.get('fromDate') && req.query.get('toDate')
            ? [req.query.get('fromDate'), req.query.get('toDate')]
            : null,
      }

      if (!organizationId) {
        return jsonResponse(
          'Missing required parameter: organizationId',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await patientService.fetchPatientsForOrganization(
        organizationId,
        searchText,
        filters,
        sortBy,
        sortOrder,
        pageSize,
        page,
      )

      return jsonResponse(result)
    } catch (error) {
      console.error('Error fetching patients for organization:', error)
      return jsonResponse(
        'Failed to fetch patients for organization',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new PatientHanlder()
