const prescriptionService = require('../services/prescription-service')
const paymentService = require('../services/payment-service')
const patientService = require('../services/patient-service')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { PaymentType } = require('../common/constant')
const productFormMapper = require('../utils/product-form-mapper')
const { v4: uuidv4 } = require('uuid')

class PrescriptionHandler {
  async getPrescriptions(req) {
    try {
      const patientId = req.query.get('patientId')
      const dateFilter = req.query.get('dateFilter') || null
      const customStartDate = req.query.get('customStartDate') || null
      const customEndDate = req.query.get('customEndDate') || null
      const searchText = req.query.get('searchText') || null

      if (!patientId) {
        return jsonResponse(
          'Missing required parameter: patientId',
          HttpStatusCode.BadRequest,
        )
      }

      const customDateRange =
        dateFilter?.trim().toLowerCase() === 'custom' &&
        customStartDate &&
        customEndDate
          ? { start: customStartDate, end: customEndDate }
          : null

      const prescriptions = await prescriptionService.getPrescriptionsByPatient(
        patientId,
        dateFilter,
        customDateRange,
        searchText,
      )

      // Transform prescriptions to use short forms
      const transformedPrescriptions =
        productFormMapper.transformPrescriptions(prescriptions)

      return jsonResponse(transformedPrescriptions)
    } catch (err) {
      return jsonResponse(
        'Error fetching prescriptions',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getPrescriptionDetails(req) {
    try {
      const prescriptionId = req.query.get('prescriptionId')
      if (!prescriptionId) {
        return jsonResponse(
          'Missing patient ID or prescription ID',
          HttpStatusCode.BadRequest,
        )
      }

      const prescription = await prescriptionService.getPrescriptionById(
        prescriptionId,
      )

      // Transform prescription to use short forms
      const transformedPrescription =
        productFormMapper.transformPrescriptions(prescription)

      return jsonResponse(transformedPrescription)
    } catch (err) {
      return jsonResponse(
        'Error fetching prescription details',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async createOrUpdatePrescription(req) {
    try {
      const body = await req.json()
      const { patientId, medicines, doctor, doctorEmail } = body

      const prescriptionId = req.query.get('prescriptionId')
      if (
        !patientId ||
        !doctor ||
        !Array.isArray(medicines) ||
        medicines.length === 0
      ) {
        return jsonResponse(
          'Invalid data for prescription',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await prescriptionService.createOrUpdatePrescriptions(
        patientId,
        doctor,
        medicines,
        prescriptionId,
        doctorEmail,
      )

      return jsonResponse(result, HttpStatusCode.Created)
    } catch (err) {
      console.error('Prescription error:', err)
      return jsonResponse(
        'Error creating or updating prescription',
        HttpStatusCode.InternalServerError,
      )
    }
  }
  async searchPrescriptions(req) {
    try {
      const body = await req.json()
      const {
        searchText = '',
        pageSize = 10,
        continuationToken = '',
        patientId = null,
      } = body

      if (!searchText.trim()) {
        return jsonResponse('Missing search text', HttpStatusCode.BadRequest)
      }

      const result = await prescriptionService.searchPrescriptions(
        searchText,
        pageSize,
        continuationToken,
        patientId,
      )

      // Transform search results to use short forms
      if (result && result.items) {
        result.items = productFormMapper.transformPrescriptions(result.items)
      }

      return jsonResponse(result)
    } catch (err) {
      console.error('Search prescriptions error:', err)
      return jsonResponse(
        'Error searching prescriptions',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Create prescription with payment integration
   * Calculates total medicine cost and creates payment order if required
   */
  async createPrescriptionWithPayment(req) {
    try {
      const body = await req.json()
      const {
        patientId,
        medicines,
        doctor,
        doctorEmail,
        requirePayment = false,
      } = body

      if (
        !patientId ||
        !doctor ||
        !Array.isArray(medicines) ||
        medicines.length === 0
      ) {
        return jsonResponse(
          'Invalid data for prescription',
          HttpStatusCode.BadRequest,
        )
      }

      // Get patient details
      const patient = await patientService.GetPatientById(patientId)
      if (!patient) {
        return jsonResponse('Patient not found', HttpStatusCode.NotFound)
      }

      // Calculate total medicine cost
      const totalMedicineCost = this.calculateMedicineCost(medicines)

      // If payment is required and total cost > 0, create payment order first
      if (requirePayment && totalMedicineCost > 0) {
        // Generate prescription ID
        const prescriptionId = uuidv4()

        // Create payment order for medicine cost
        const paymentData = {
          amount: totalMedicineCost, // Amount in rupees
          currency: 'INR',
          paymentType: PaymentType.PRESCRIPTION,
          patientId: patient.id,
          organizationId: patient.organizationId,
          description: `Prescription Medicines for ${patient.name}`,
          metadata: {
            prescriptionId: prescriptionId,
            patientName: patient.name,
            doctorName: doctor,
            medicineCount: medicines.length,
            totalCost: totalMedicineCost,
          },
        }

        const paymentOrder = await paymentService.createOrder(paymentData)

        // Return payment order details for frontend to process
        return jsonResponse({
          requiresPayment: true,
          totalMedicineCost: totalMedicineCost,
          paymentOrder: paymentOrder.data,
          prescriptionId: prescriptionId,
          medicineCount: medicines.length,
          message: 'Payment required for prescription medicines',
        })
      } else {
        // No payment required, create prescription directly
        return await this.createOrUpdatePrescription(req)
      }
    } catch (err) {
      console.error('Prescription with payment error:', err)
      return jsonResponse(
        'Error creating prescription with payment',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Complete prescription creation after successful payment
   * Called after payment verification is successful
   */
  async completePrescriptionCreation(req) {
    try {
      const body = await req.json()
      const {
        patientId,
        medicines,
        doctor,
        doctorEmail,
        paymentId,
        prescriptionId,
      } = body

      if (!paymentId) {
        return jsonResponse('Payment ID is required', HttpStatusCode.BadRequest)
      }

      // Verify payment is completed
      const payment = await paymentService.getPaymentById(paymentId)
      if (!payment || !payment.isSuccessful()) {
        return jsonResponse(
          'Payment not completed or not found',
          HttpStatusCode.BadRequest,
        )
      }

      // Ensure prescription data matches payment metadata
      if (
        payment.metadata.prescriptionId &&
        prescriptionId !== payment.metadata.prescriptionId
      ) {
        return jsonResponse(
          'Prescription ID mismatch with payment record',
          HttpStatusCode.BadRequest,
        )
      }

      // Create prescription with the provided prescription ID
      const prescriptionData = {
        patientId,
        medicines,
        doctor,
        doctorEmail,
        id: prescriptionId || payment.metadata.prescriptionId,
      }

      const result = await prescriptionService.createOrUpdatePrescriptions(
        patientId,
        doctor,
        medicines,
        prescriptionData.id,
        doctorEmail,
      )

      return jsonResponse(
        {
          success: true,
          prescription: result,
          paymentId: paymentId,
          message: 'Prescription created successfully with payment',
        },
        HttpStatusCode.Created,
      )
    } catch (err) {
      console.error('Complete prescription creation error:', err)
      return jsonResponse(
        'Error completing prescription creation',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Calculate total cost of medicines in a prescription
   * Helper method to compute payment amount
   */
  calculateMedicineCost(medicines) {
    let totalCost = 0

    medicines.forEach((medicine) => {
      if (medicine.cost && !isNaN(parseFloat(medicine.cost))) {
        const quantity = parseInt(medicine.quantity) || 1
        const unitCost = parseFloat(medicine.cost)
        totalCost += unitCost * quantity
      }
    })

    return Math.round(totalCost * 100) / 100 // Round to 2 decimal places
  }

  /**
   * Get prescription cost estimate
   * Helper method for frontend to display cost before payment
   */
  async getPrescriptionCostEstimate(req) {
    try {
      const body = await req.json()
      const { medicines } = body

      if (!Array.isArray(medicines) || medicines.length === 0) {
        return jsonResponse('Invalid medicines data', HttpStatusCode.BadRequest)
      }

      const totalCost = this.calculateMedicineCost(medicines)
      const medicineBreakdown = medicines.map((medicine) => ({
        name: medicine.brandName || medicine.genericName,
        quantity: parseInt(medicine.quantity) || 1,
        unitCost: parseFloat(medicine.cost) || 0,
        totalCost:
          (parseFloat(medicine.cost) || 0) * (parseInt(medicine.quantity) || 1),
      }))

      return jsonResponse({
        totalCost: totalCost,
        medicineCount: medicines.length,
        medicineBreakdown: medicineBreakdown,
        requiresPayment: totalCost > 0,
      })
    } catch (err) {
      console.error('Get prescription cost estimate error:', err)
      return jsonResponse(
        'Error calculating prescription cost',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}
module.exports = new PrescriptionHandler()
