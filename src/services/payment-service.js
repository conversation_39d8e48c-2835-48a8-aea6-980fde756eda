const Razorpay = require('razorpay')
const crypto = require('crypto')
const { v4: uuidv4 } = require('uuid')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const PaymentModel = require('../models/payment-model')
const { PaymentStatus, PaymentType } = require('../common/constant')
const logging = require('../common/logging')

const paymentsContainer = 'Payments'

class PaymentService {
  constructor() {
    // Initialize Razorpay instance
    this.razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID,
      key_secret: process.env.RAZORPAY_KEY_SECRET,
    })

    this.webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET
  }

  /**
   * Create a Razorpay order and save payment record
   * @param {Object} paymentData - Payment data
   * @returns {Object} - Order details with payment ID
   */
  async createOrder(paymentData) {
    try {
      // Validate payment data
      const payment = new PaymentModel(paymentData)

      // Create Razorpay order
      const orderOptions = {
        amount: payment.amount, // Amount in paise
        currency: payment.currency,
        receipt: `receipt_${Date.now()}`,
        notes: {
          paymentType: payment.paymentType,
          patientId: payment.patientId,
          organizationId: payment.organizationId,
          description: payment.description,
          ...payment.metadata,
        },
      }

      const razorpayOrder = await this.razorpay.orders.create(orderOptions)

      // Update payment with Razorpay order ID
      payment.razorpayOrderId = razorpayOrder.id
      payment.id = uuidv4()

      // Save payment record to database
      const savedPayment = await cosmosDbContext.createItem(
        payment,
        paymentsContainer,
      )

      return {
        success: true,
        data: {
          orderId: razorpayOrder.id,
          paymentId: savedPayment.id,
          keyId: process.env.RAZORPAY_KEY_ID,
          amount: payment.amount,
          currency: payment.currency,
          status: payment.status,
          description: payment.description,
        },
      }
    } catch (error) {
      logging.logError('Error creating payment order', error)
      throw new Error(`Failed to create payment order: ${error.message}`)
    }
  }

  /**
   * Verify Razorpay payment signature
   * @param {Object} verificationData - Razorpay verification data
   * @returns {Object} - Verification result
   */
  async verifyPayment(verificationData) {
    try {
      const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
        verificationData

      // Generate expected signature
      const body = razorpay_order_id + '|' + razorpay_payment_id
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
        .update(body.toString())
        .digest('hex')

      const isSignatureValid = expectedSignature === razorpay_signature

      if (isSignatureValid) {
        // Find payment record by Razorpay order ID
        const query = `SELECT * FROM c WHERE c.razorpayOrderId = '${razorpay_order_id}'`
        const payments = await cosmosDbContext.queryItems(
          query,
          paymentsContainer,
        )

        if (payments.length === 0) {
          throw new Error('Payment record not found')
        }

        const payment = payments[0]

        // Update payment status
        payment.status = PaymentStatus.COMPLETED
        payment.razorpayPaymentId = razorpay_payment_id
        payment.razorpaySignature = razorpay_signature
        payment.verifiedAt = new Date().toISOString()

        await cosmosDbContext.updateItem(payment, paymentsContainer)

        return {
          success: true,
          verified: true,
          message: 'Payment verified successfully',
          paymentId: razorpay_payment_id,
          payment: payment,
        }
      } else {
        return {
          success: false,
          verified: false,
          message: 'Invalid payment signature',
        }
      }
    } catch (error) {
      logging.logError('Error verifying payment', error)
      throw new Error(`Payment verification failed: ${error.message}`)
    }
  }

  /**
   * Handle Razorpay webhook events
   * @param {Object} webhookData - Webhook payload
   * @param {String} signature - Webhook signature
   * @returns {Object} - Processing result
   */
  async handleWebhook(webhookData, signature) {
    try {
      // Verify webhook signature
      const expectedSignature = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(JSON.stringify(webhookData))
        .digest('hex')

      if (expectedSignature !== signature) {
        throw new Error('Invalid webhook signature')
      }

      const { event, payload } = webhookData
      const { payment, order } = payload

      // Find payment record
      const query = `SELECT * FROM c WHERE c.razorpayOrderId = '${order.id}'`
      const payments = await cosmosDbContext.queryItems(
        query,
        paymentsContainer,
      )

      if (payments.length === 0) {
        logging.logError(`Payment record not found for order ID: ${order.id}`)
        return { success: false, message: 'Payment record not found' }
      }

      const paymentRecord = payments[0]

      // Handle different webhook events
      switch (event) {
        case 'payment.captured':
          paymentRecord.status = PaymentStatus.COMPLETED
          paymentRecord.razorpayPaymentId = payment.id
          paymentRecord.verifiedAt = new Date().toISOString()
          break

        case 'payment.failed':
          paymentRecord.status = PaymentStatus.FAILED
          paymentRecord.failureReason =
            payment.error_description || 'Payment failed'
          break

        default:
          logging.logInfo(`Unhandled webhook event: ${event}`)
          return { success: true, message: 'Event not processed' }
      }

      // Update payment record
      await cosmosDbContext.updateItem(paymentRecord, paymentsContainer)

      return {
        success: true,
        message: `Webhook processed for event: ${event}`,
        paymentId: paymentRecord.id,
      }
    } catch (error) {
      logging.logError('Error handling webhook', error)
      throw new Error(`Webhook processing failed: ${error.message}`)
    }
  }

  /**
   * Get payment details by ID
   * @param {String} paymentId - Payment ID
   * @returns {Object} - Payment details
   */
  async getPaymentById(paymentId) {
    try {
      const query = `SELECT * FROM c WHERE c.id = '${paymentId}'`
      const payments = await cosmosDbContext.queryItems(
        query,
        paymentsContainer,
      )

      if (payments.length === 0) {
        throw new Error('Payment not found')
      }

      return payments[0]
    } catch (error) {
      logging.logError('Error fetching payment details', error)
      throw new Error(`Failed to fetch payment: ${error.message}`)
    }
  }

  /**
   * Get payments by organization with pagination
   * @param {String} organizationId - Organization ID
   * @param {Number} pageSize - Page size
   * @param {String} continuationToken - Continuation token
   * @returns {Object} - Paginated payments
   */
  async getPaymentsByOrganization(
    organizationId,
    pageSize = 20,
    continuationToken = null,
  ) {
    try {
      const query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}' ORDER BY c.createdAt DESC`

      const options = {
        maxItemCount: pageSize,
      }

      if (continuationToken) {
        options.continuationToken = continuationToken
      }

      const result = await cosmosDbContext.queryItemsWithPagination(
        query,
        paymentsContainer,
        options,
      )

      return {
        payments: result.resources,
        continuationToken: result.continuationToken,
        hasMoreResults: !!result.continuationToken,
        currentPage: Math.ceil(result.resources.length / pageSize),
        pageSize: pageSize,
        totalFetched: result.resources.length,
      }
    } catch (error) {
      logging.logError('Error fetching organization payments', error)
      throw new Error(`Failed to fetch payments: ${error.message}`)
    }
  }

  /**
   * Get payment statistics for an organization
   * @param {String} organizationId - Organization ID
   * @returns {Object} - Payment statistics
   */
  async getPaymentStatistics(organizationId) {
    try {
      // Get all completed payments for the organization
      const query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}' AND c.status = '${PaymentStatus.COMPLETED}'`
      const payments = await cosmosDbContext.queryItems(
        query,
        paymentsContainer,
      )

      // Calculate statistics
      const totalAmount = payments.reduce(
        (sum, payment) => sum + payment.amount,
        0,
      )
      const totalCount = payments.length

      // Group by payment type
      const paymentTypeStats = {}
      payments.forEach((payment) => {
        if (!paymentTypeStats[payment.paymentType]) {
          paymentTypeStats[payment.paymentType] = {
            count: 0,
            amount: 0,
          }
        }
        paymentTypeStats[payment.paymentType].count++
        paymentTypeStats[payment.paymentType].amount += payment.amount
      })

      // Group by month
      const monthlyStats = {}
      payments.forEach((payment) => {
        const month = new Date(payment.createdAt).toISOString().substring(0, 7) // YYYY-MM
        if (!monthlyStats[month]) {
          monthlyStats[month] = {
            count: 0,
            amount: 0,
          }
        }
        monthlyStats[month].count++
        monthlyStats[month].amount += payment.amount
      })

      return {
        totalAmount: totalAmount / 100, // Convert to rupees
        totalCount,
        paymentTypeBreakdown: Object.keys(paymentTypeStats).map((type) => ({
          type,
          count: paymentTypeStats[type].count,
          amount: paymentTypeStats[type].amount / 100, // Convert to rupees
        })),
        monthlyBreakdown: Object.keys(monthlyStats)
          .map((month) => ({
            month,
            count: monthlyStats[month].count,
            amount: monthlyStats[month].amount / 100, // Convert to rupees
          }))
          .sort((a, b) => a.month.localeCompare(b.month)),
      }
    } catch (error) {
      logging.logError('Error fetching payment statistics', error)
      throw new Error(`Failed to fetch payment statistics: ${error.message}`)
    }
  }

  /**
   * Update payment status (for manual updates)
   * @param {String} paymentId - Payment ID
   * @param {String} status - New status
   * @param {String} reason - Reason for status change
   * @returns {Object} - Updated payment
   */
  async updatePaymentStatus(paymentId, status, reason = null) {
    try {
      const payment = await this.getPaymentById(paymentId)

      payment.status = status
      if (reason) {
        payment.failureReason = reason
      }

      const updatedPayment = await cosmosDbContext.updateItem(
        payment,
        paymentsContainer,
      )
      return updatedPayment
    } catch (error) {
      logging.logError('Error updating payment status', error)
      throw new Error(`Failed to update payment status: ${error.message}`)
    }
  }
}

module.exports = new PaymentService()
