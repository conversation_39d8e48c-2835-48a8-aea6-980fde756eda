const msal = require('@azure/msal-node')
const { Client } = require('@microsoft/microsoft-graph-client')
const { logError, logInfo } = require('../common/logging')
const helper = require('../common/helper')

const config = {
  auth: {
    clientId: process.env.CLIENT_ID,
    authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
    clientSecret: process.env.CLIENT_SECRET,
  },
}

const cca = new msal.ConfidentialClientApplication(config)
class GraphService {
  async getToken() {
    try {
      const tokenResponse = await cca.acquireTokenByClientCredential({
        scopes: ['https://graph.microsoft.com/.default'],
      })
      return tokenResponse
    } catch (error) {
      logError(`Unable to get token :: `, error)
      return null
    }
  }

  async createUser(accessToken, user) {
    try {
      logInfo(`Creating user :: ${JSON.stringify(user)}`)
      const client = Client.init({
        authProvider: (done) => {
          done(null, accessToken) // Pass the token to the auth provider
        },
      })

      var data = await client.api('/users').post(user)
      return data
    } catch (error) {
      if (error.statusCode === 409) {
        logError(
          `Unable to create user: User with userPrincipalName ${user.userPrincipalName} already exists.`,
          error,
        )
      } else {
        logError(`Unable to create user`, error)
      }
      throw error // Re-throw the error for further handling
    }
  }

  async getUserByPrincipalName(accessToken, userPrincipalName) {
    try {
      const client = Client.init({
        authProvider: (done) => {
          done(null, accessToken)
        },
      })

      const user = await client.api(`/users/${userPrincipalName}`).get()
      return user
    } catch (error) {
      if (error.statusCode === 404) {
        console.log(
          `User with principal name ${userPrincipalName} does not exist.`,
        )
        return null // User does not exist
      }
      console.error(
        `Error fetching user with principal name ${userPrincipalName}:`,
        error,
      )
      throw error // Re-throw other errors
    }
  }

  async updateUser(accessToken, userId, updateData) {
    try {
      logInfo(
        `Updating B2C user: ${userId} with data: ${JSON.stringify(updateData)}`,
      )

      const client = Client.init({
        authProvider: (done) => {
          done(null, accessToken)
        },
      })

      const b2cUpdateData = {}
      if (updateData.name) {
        b2cUpdateData.displayName = updateData.name
      }

      if (Object.keys(b2cUpdateData).length === 0) {
        logInfo('No B2C fields to update')
        return { success: true, message: 'No B2C fields to update' }
      }

      const result = await client.api(`/users/${userId}`).patch(b2cUpdateData)
      logInfo(`B2C user updated successfully: ${userId}`)
      return result
    } catch (error) {
      logError(`Error updating B2C user ${userId}:`, error)
      throw error
    }
  }

  async deleteUser(accessToken, userId) {
    try {
      logInfo(`Deleting B2C user: ${userId}`)

      const client = Client.init({
        authProvider: (done) => {
          done(null, accessToken)
        },
      })

      await client.api(`/users/${userId}`).delete()
      logInfo(`B2C user deleted successfully: ${userId}`)
      return { success: true, message: 'User deleted from B2C successfully' }
    } catch (error) {
      logError(`Error deleting B2C user ${userId}:`, error)
      throw error
    }
  }

  async resetUserPassword(accessToken, userId, newPassword) {
    try {
      logInfo(`Resetting password for B2C user: ${userId}`)

      const client = Client.init({
        authProvider: (done) => {
          done(null, accessToken)
        },
      })

      const passwordProfile = {
        forceChangePasswordNextSignIn: false,
        password: newPassword
      }

      const result = await client.api(`/users/${userId}`).patch({
        passwordProfile: passwordProfile
      })

      logInfo(`B2C user password reset successfully: ${userId}`)
      return result
    } catch (error) {
      logError(`Error resetting password for B2C user ${userId}:`, error)
      throw error
    }
  }

  async getUserByEmail(accessToken, email) {
    try {
      logInfo(`Fetching B2C user by email: ${email}`)

      const client = Client.init({
        authProvider: (done) => {
          done(null, accessToken)
        },
      })

      // Search for user by email using filter
      const users = await client.api('/users').filter(`identities/any(c:c/issuerAssignedId eq '${email}' and c/issuer eq '${process.env.TENANT_NAME}.onmicrosoft.com')`).get()

      if (users.value && users.value.length > 0) {
        logInfo(`B2C user found by email: ${email}`)
        return users.value[0]
      }

      logInfo(`B2C user not found by email: ${email}`)
      return null
    } catch (error) {
      logError(`Error fetching B2C user by email ${email}:`, error)
      throw error
    }
  }
}

module.exports = new GraphService()
