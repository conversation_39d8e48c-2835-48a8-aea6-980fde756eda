const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const { PaymentStatus } = require('../common/constant')

const paymentsContainer = 'Payments'

class PaymentRepository {
  /**
   * Create a new payment record
   * @param {Object} payment - Payment data
   * @returns {Object} - Created payment
   */
  async createPayment(payment) {
    try {
      return await cosmosDbContext.createItem(payment, paymentsContainer)
    } catch (error) {
      logging.logError('Error creating payment record', error)
      throw new Error(`Failed to create payment: ${error.message}`)
    }
  }

  /**
   * Update payment record
   * @param {Object} payment - Payment data
   * @returns {Object} - Updated payment
   */
  async updatePayment(payment) {
    try {
      return await cosmosDbContext.updateItem(payment, paymentsContainer)
    } catch (error) {
      logging.logError('Error updating payment record', error)
      throw new Error(`Failed to update payment: ${error.message}`)
    }
  }

  /**
   * Get payment by ID
   * @param {String} paymentId - Payment ID
   * @returns {Object} - Payment record
   */
  async getPaymentById(paymentId) {
    try {
      const query = `SELECT * FROM c WHERE c.id = '${paymentId}'`
      const payments = await cosmosDbContext.queryItems(query, paymentsContainer)
      
      if (payments.length === 0) {
        return null
      }
      
      return payments[0]
    } catch (error) {
      logging.logError('Error fetching payment by ID', error)
      throw new Error(`Failed to fetch payment: ${error.message}`)
    }
  }

  /**
   * Get payment by Razorpay order ID
   * @param {String} razorpayOrderId - Razorpay order ID
   * @returns {Object} - Payment record
   */
  async getPaymentByRazorpayOrderId(razorpayOrderId) {
    try {
      const query = `SELECT * FROM c WHERE c.razorpayOrderId = '${razorpayOrderId}'`
      const payments = await cosmosDbContext.queryItems(query, paymentsContainer)
      
      if (payments.length === 0) {
        return null
      }
      
      return payments[0]
    } catch (error) {
      logging.logError('Error fetching payment by Razorpay order ID', error)
      throw new Error(`Failed to fetch payment: ${error.message}`)
    }
  }

  /**
   * Get payments by patient ID
   * @param {String} patientId - Patient ID
   * @param {String} status - Payment status filter (optional)
   * @returns {Array} - Payment records
   */
  async getPaymentsByPatientId(patientId, status = null) {
    try {
      let query = `SELECT * FROM c WHERE c.patientId = '${patientId}'`
      
      if (status) {
        query += ` AND c.status = '${status}'`
      }
      
      query += ` ORDER BY c.createdAt DESC`
      
      return await cosmosDbContext.queryItems(query, paymentsContainer)
    } catch (error) {
      logging.logError('Error fetching payments by patient ID', error)
      throw new Error(`Failed to fetch patient payments: ${error.message}`)
    }
  }

  /**
   * Get payments by organization ID with pagination
   * @param {String} organizationId - Organization ID
   * @param {Number} pageSize - Page size
   * @param {String} continuationToken - Continuation token
   * @param {String} status - Payment status filter (optional)
   * @returns {Object} - Paginated payment records
   */
  async getPaymentsByOrganizationId(organizationId, pageSize = 20, continuationToken = null, status = null) {
    try {
      let query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}'`
      
      if (status) {
        query += ` AND c.status = '${status}'`
      }
      
      query += ` ORDER BY c.createdAt DESC`
      
      const options = {
        maxItemCount: pageSize
      }
      
      if (continuationToken) {
        options.continuationToken = continuationToken
      }
      
      const result = await cosmosDbContext.queryItemsWithPagination(query, paymentsContainer, options)
      
      return {
        payments: result.resources,
        continuationToken: result.continuationToken,
        hasMoreResults: !!result.continuationToken,
        totalFetched: result.resources.length
      }
    } catch (error) {
      logging.logError('Error fetching payments by organization ID', error)
      throw new Error(`Failed to fetch organization payments: ${error.message}`)
    }
  }

  /**
   * Get payment statistics for organization
   * @param {String} organizationId - Organization ID
   * @param {String} startDate - Start date (optional)
   * @param {String} endDate - End date (optional)
   * @returns {Array} - Payment records for statistics
   */
  async getPaymentStatistics(organizationId, startDate = null, endDate = null) {
    try {
      let query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}' AND c.status = '${PaymentStatus.COMPLETED}'`
      
      if (startDate && endDate) {
        query += ` AND c.createdAt >= '${startDate}' AND c.createdAt <= '${endDate}'`
      }
      
      return await cosmosDbContext.queryItems(query, paymentsContainer)
    } catch (error) {
      logging.logError('Error fetching payment statistics', error)
      throw new Error(`Failed to fetch payment statistics: ${error.message}`)
    }
  }

  /**
   * Get payments by payment type
   * @param {String} organizationId - Organization ID
   * @param {String} paymentType - Payment type
   * @param {String} status - Payment status filter (optional)
   * @returns {Array} - Payment records
   */
  async getPaymentsByType(organizationId, paymentType, status = null) {
    try {
      let query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}' AND c.paymentType = '${paymentType}'`
      
      if (status) {
        query += ` AND c.status = '${status}'`
      }
      
      query += ` ORDER BY c.createdAt DESC`
      
      return await cosmosDbContext.queryItems(query, paymentsContainer)
    } catch (error) {
      logging.logError('Error fetching payments by type', error)
      throw new Error(`Failed to fetch payments by type: ${error.message}`)
    }
  }

  /**
   * Get payments by metadata field
   * @param {String} organizationId - Organization ID
   * @param {String} metadataKey - Metadata key
   * @param {String} metadataValue - Metadata value
   * @returns {Array} - Payment records
   */
  async getPaymentsByMetadata(organizationId, metadataKey, metadataValue) {
    try {
      const query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}' AND c.metadata.${metadataKey} = '${metadataValue}' ORDER BY c.createdAt DESC`
      
      return await cosmosDbContext.queryItems(query, paymentsContainer)
    } catch (error) {
      logging.logError('Error fetching payments by metadata', error)
      throw new Error(`Failed to fetch payments by metadata: ${error.message}`)
    }
  }

  /**
   * Delete payment record (soft delete by updating status)
   * @param {String} paymentId - Payment ID
   * @returns {Object} - Updated payment
   */
  async deletePayment(paymentId) {
    try {
      const payment = await this.getPaymentById(paymentId)
      
      if (!payment) {
        throw new Error('Payment not found')
      }
      
      payment.status = PaymentStatus.CANCELLED
      payment.updatedAt = new Date().toISOString()
      
      return await this.updatePayment(payment)
    } catch (error) {
      logging.logError('Error deleting payment', error)
      throw new Error(`Failed to delete payment: ${error.message}`)
    }
  }

  /**
   * Search payments with filters
   * @param {Object} filters - Search filters
   * @returns {Array} - Payment records
   */
  async searchPayments(filters) {
    try {
      let query = 'SELECT * FROM c WHERE 1=1'
      
      if (filters.organizationId) {
        query += ` AND c.organizationId = '${filters.organizationId}'`
      }
      
      if (filters.patientId) {
        query += ` AND c.patientId = '${filters.patientId}'`
      }
      
      if (filters.status) {
        query += ` AND c.status = '${filters.status}'`
      }
      
      if (filters.paymentType) {
        query += ` AND c.paymentType = '${filters.paymentType}'`
      }
      
      if (filters.startDate && filters.endDate) {
        query += ` AND c.createdAt >= '${filters.startDate}' AND c.createdAt <= '${filters.endDate}'`
      }
      
      if (filters.minAmount) {
        query += ` AND c.amount >= ${filters.minAmount}`
      }
      
      if (filters.maxAmount) {
        query += ` AND c.amount <= ${filters.maxAmount}`
      }
      
      query += ` ORDER BY c.createdAt DESC`
      
      return await cosmosDbContext.queryItems(query, paymentsContainer)
    } catch (error) {
      logging.logError('Error searching payments', error)
      throw new Error(`Failed to search payments: ${error.message}`)
    }
  }
}

module.exports = new PaymentRepository()
